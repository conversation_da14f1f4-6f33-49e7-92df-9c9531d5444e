"use client";

import { useEffect, useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';

interface ParallaxElementProps {
  children: React.ReactNode;
  speed?: number;
  className?: string;
  direction?: 'up' | 'down';
}

export default function ParallaxElement({
  children,
  speed = 0.5,
  className = '',
  direction = 'up'
}: ParallaxElementProps) {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  });

  const y = useTransform(
    scrollYProgress,
    [0, 1],
    direction === 'up' ? [0, -speed * 50] : [0, speed * 50]
  );

  return (
    <motion.div
      ref={ref}
      style={{ y }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Specialized parallax components
export function ParallaxImage({
  src,
  alt,
  className = '',
  speed = 0.5
}: {
  src: string;
  alt: string;
  className?: string;
  speed?: number;
}) {
  return (
    <ParallaxElement speed={speed} className={className}>
      <img src={src} alt={alt} className="w-full h-full object-cover" />
    </ParallaxElement>
  );
}

export function ParallaxText({
  children,
  className = '',
  speed = 0.3
}: {
  children: React.ReactNode;
  className?: string;
  speed?: number;
}) {
  return (
    <ParallaxElement speed={speed} className={className}>
      {children}
    </ParallaxElement>
  );
}
