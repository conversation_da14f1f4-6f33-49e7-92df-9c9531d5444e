"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/AboutSection.tsx":
/*!**************************************************!*\
  !*** ./src/components/sections/AboutSection.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AboutSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Cloud_Code2_Container_Database_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Cloud,Code2,Container,Database,Heart!=!lucide-react */ \"(app-pages-browser)/../../../node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Cloud_Code2_Container_Database_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Cloud,Code2,Container,Database,Heart!=!lucide-react */ \"(app-pages-browser)/../../../node_modules/lucide-react/dist/esm/icons/container.js\");\n/* harmony import */ var _barrel_optimize_names_Cloud_Code2_Container_Database_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Cloud,Code2,Container,Database,Heart!=!lucide-react */ \"(app-pages-browser)/../../../node_modules/lucide-react/dist/esm/icons/cloud.js\");\n/* harmony import */ var _barrel_optimize_names_Cloud_Code2_Container_Database_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Cloud,Code2,Container,Database,Heart!=!lucide-react */ \"(app-pages-browser)/../../../node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Cloud_Code2_Container_Database_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Cloud,Code2,Container,Database,Heart!=!lucide-react */ \"(app-pages-browser)/../../../node_modules/lucide-react/dist/esm/icons/code-xml.js\");\n\n\n\n// Technology icons component\nconst TechIcon = (param)=>{\n    let { name } = param;\n    const iconMap = {\n        'React': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-full h-full\",\n            viewBox: \"0 0 24 24\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 10.11c1.03 0 1.87.84 1.87 1.89s-.84 1.89-1.87 1.89c-1.03 0-1.87-.84-1.87-1.89s.84-1.89 1.87-1.89M7.37 20c.63.38 2.01-.2 3.6-1.7-.52-.59-1.03-1.23-1.51-1.9a22.7 22.7 0 0 1-2.4-.36c-.51 2.14-.32 3.61.31 3.96m.71-5.74l-.29-.51c-.11.29-.22.58-.29.86.27.06.57.11.88.16l-.3-.51m6.54-.76l.81-1.5-.81-1.5c-.3-.53-.62-1-.91-1.47C13.17 9 12.6 9 12 9s-1.17 0-1.71.03c-.29.47-.61.94-.91 1.47L8.57 12l.81 1.5c.3.53.62 1 .91 1.47.54.03 1.11.03 1.71.03s1.17 0 1.71-.03c.29-.47.61-.94.91-1.47M12 6.78c-.19.22-.39.45-.59.72h1.18c-.2-.27-.4-.5-.59-.72m0 10.44c.19-.22.39-.45.59-.72h-1.18c.2.27.4.5.59.72M16.62 4c-.62-.38-2 .2-3.59 1.7.52.59 1.03 1.23 1.51 1.9.82.08 1.63.2 2.4.36.51-2.14.32-3.61-.32-3.96m-.7 5.74l.29.51c.11-.29.22-.58.29-.86-.27-.06-.57-.11-.88-.16l.3.51m1.45-7.05c1.47.84 1.63 3.05 1.01 5.63 2.54.75 4.37 1.99 4.37 3.68s-1.83 2.93-4.37 3.68c.62 2.58.46 4.79-1.01 5.63-1.46.84-3.45-.12-5.37-1.95-1.92 1.83-3.91 2.79-5.37 1.95-1.47-.84-1.63-3.05-1.01-5.63-2.54-.75-4.37-1.99-4.37-3.68s1.83-2.93 4.37-3.68c-.62-2.58-.46-4.79 1.01-5.63 1.46-.84 3.45.12 5.37 1.95 1.92-1.83 3.91-2.79 5.37-1.95z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                lineNumber: 16,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, undefined),\n        'Next.js': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            viewBox: \"0 0 24 24\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M11.5725 0c-.1763 0-.3098.0013-.3584.0067-.0516.0053-.2159.021-.3636.0328-3.4088.3073-6.6017 2.1463-8.624 4.9728C1.1004 6.584.3802 8.3666.1082 10.255c-.0962.659-.108.8537-.108 1.7474s.012 1.0884.108 1.7476c.652 4.506 3.8591 8.2919 8.2087 9.6945.7789.2511 1.6.4223 2.5337.5255.3636.04 1.9354.04 2.299 0 1.6117-.1783 2.9772-.577 4.3237-1.2643.2065-.1056.2464-.1337.2183-.1573-.0188-.0139-.8987-1.1938-1.9543-2.62l-1.919-2.592-2.4047-3.5583c-1.3231-1.9564-2.4117-3.556-2.4211-3.556-.0094-.0026-.0187 1.5787-.0235 3.509-.0067 3.3802-.0093 3.5162-.0516 3.596-.061.115-.108.1618-.2064.2134-.075.0374-.1408.0445-.5429.0445h-.4570l-.0803-.0516c-.0516-.0336-.0939-.0822-.1145-.1262l-.0281-.0723.0188-4.6901.0235-4.6901.0375-.0751c.0233-.0516.0751-.1171.1138-.1503.0561-.047.0994-.0517.4665-.0517.4570 0 .5429.0141.6570.0938.0328.0235 1.3457 2.0186 2.915 4.4317l2.8544 4.3846 1.9107 2.9057 1.9107 2.9057.0423-.0281c.7939-.5194 1.5329-1.1477 2.1665-1.8438 1.6977-1.8729 2.7387-4.0172 3.0845-6.3581.0962-.659.108-.8537.108-1.7474s-.012-1.0884-.108-1.7476C22.8982 4.2625 19.6711.4766 15.3217-.9261c-.7672-.2487-1.5739-.4172-2.4985-.5232-.3389-.0388-1.9321-.0388-2.2710.0000z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, undefined),\n        'TypeScript': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            viewBox: \"0 0 24 24\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M1.125 0C.502 0 0 .502 0 1.125v21.75C0 23.498.502 24 1.125 24h21.75c.623 0 1.125-.502 1.125-1.125V1.125C24 .502 23.498 0 22.875 0zm17.363 9.75c.612 0 1.154.037 1.627.111a6.38 6.38 0 0 1 1.306.34v2.458a3.95 3.95 0 0 0-.643-.361 5.093 5.093 0 0 0-.717-.26 5.453 5.453 0 0 0-1.426-.2c-.3 0-.573.028-.819.086a2.1 2.1 0 0 0-.623.242c-.17.104-.3.229-.393.374a.888.888 0 0 0-.14.49c0 .196.053.373.156.529.104.156.252.304.443.444s.423.276.696.41c.273.135.582.274.926.416.47.197.892.407 1.266.628.374.222.695.473.963.753.268.279.472.598.614.957.142.359.214.776.214 1.253 0 .657-.125 1.21-.373 1.656a3.033 3.033 0 0 1-1.012 1.085 4.38 4.38 0 0 1-1.487.596c-.566.12-1.163.18-1.79.18a9.916 9.916 0 0 1-1.84-.164 5.544 5.544 0 0 1-1.512-.493v-2.63a5.033 5.033 0 0 0 3.237 1.2c.333 0 .624-.03.872-.09.249-.06.456-.144.623-.25.166-.108.29-.234.373-.38a1.023 1.023 0 0 0-.074-1.089 2.12 2.12 0 0 0-.537-.5 5.597 5.597 0 0 0-.807-.444 27.72 27.72 0 0 0-1.007-.436c-.918-.383-1.602-.852-2.053-1.405-.45-.553-.676-1.222-.676-2.005 0-.614.123-1.141.369-1.582.246-.441.58-.804 1.004-1.089a4.494 4.494 0 0 1 1.47-.629 7.536 7.536 0 0 1 1.77-.201zm-15.113.188h9.563v2.166H9.506v9.646H6.789v-9.646H3.375z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                lineNumber: 26,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined),\n        'Node.js': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            viewBox: \"0 0 24 24\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M11.998,24c-0.321,0-0.641-0.084-0.922-0.247l-2.936-1.737c-0.438-0.245-0.224-0.332-0.08-0.383 c0.585-0.203,0.703-0.25,1.328-0.604c0.065-0.037,0.151-0.023,0.218,0.017l2.256,1.339c0.082,0.045,0.197,0.045,0.272,0l8.795-5.076 c0.082-0.047,0.134-0.141,0.134-0.238V6.921c0-0.099-0.053-0.192-0.137-0.242l-8.791-5.072c-0.081-0.047-0.189-0.047-0.271,0 L3.075,6.68C2.99,6.729,2.936,6.825,2.936,6.921v10.15c0,0.097,0.054,0.189,0.139,0.235l2.409,1.392 c1.307,0.654,2.108-0.116,2.108-0.89V7.787c0-0.142,0.114-0.253,0.256-0.253h1.115c0.139,0,0.255,0.112,0.255,0.253v10.021 c0,1.745-0.95,2.745-2.604,2.745c-0.508,0-0.909,0-2.026-0.551L2.28,18.675c-0.57-0.329-0.922-0.945-0.922-1.604V6.921 c0-0.659,0.353-1.275,0.922-1.603l8.795-5.082c0.557-0.315,1.296-0.315,1.848,0l8.794,5.082c0.570,0.329,0.924,0.944,0.924,1.603 v10.15c0,0.659-0.354,1.273-0.924,1.604l-8.794,5.078C12.643,23.916,12.324,24,11.998,24z M19.099,13.993 c0-1.9-1.284-2.406-3.987-2.763c-2.731-0.361-3.009-0.548-3.009-1.187c0-0.528,0.235-1.233,2.258-1.233 c1.807,0,2.473,0.389,2.747,1.607c0.024,0.115,0.129,0.199,0.247,0.199h1.141c0.071,0,0.138-0.031,0.186-0.081 c0.048-0.054,0.074-0.123,0.067-0.196c-0.177-2.098-1.571-3.076-4.388-3.076c-2.508,0-4.004,1.058-4.004,2.833 c0,1.925,1.488,2.457,3.895,2.695c2.88,0.282,3.103,0.703,3.103,1.269c0,0.983-0.789,1.402-2.642,1.402 c-2.327,0-2.839-0.584-3.011-1.742c-0.02-0.124-0.126-0.215-0.253-0.215h-1.137c-0.141,0-0.254,0.112-0.254,0.253 c0,1.482,0.806,3.248,4.655,3.248C17.501,17.007,19.099,15.91,19.099,13.993z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, undefined),\n        'Python': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            viewBox: \"0 0 24 24\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M14.25.18l.9.2.73.26.59.3.45.32.34.34.25.34.16.33.1.3.04.26.02.2-.01.13V8.5l-.05.63-.13.55-.21.46-.26.38-.3.31-.33.25-.35.19-.35.14-.33.1-.3.07-.26.04-.21.02H8.77l-.69.05-.59.14-.5.22-.41.27-.33.32-.27.35-.2.36-.15.37-.1.35-.07.32-.04.27-.02.21v3.06H3.17l-.21-.03-.28-.07-.32-.12-.35-.18-.36-.26-.36-.36-.35-.46-.32-.59-.28-.73-.21-.88-.14-1.05-.05-1.23.06-1.22.16-1.04.24-.87.32-.71.36-.57.4-.44.42-.33.42-.24.4-.16.36-.1.32-.05.24-.01h.16l.06.01h8.16v-.83H6.18l-.01-2.75-.02-.37.05-.34.11-.31.17-.28.25-.26.31-.23.38-.2.44-.18.51-.15.58-.12.64-.1.71-.06.77-.04.84-.02 1.27.05zm-6.3 1.98l-.23.33-.08.41.08.41.23.34.33.22.41.09.41-.09.33-.22.23-.34.08-.41-.08-.41-.23-.33-.33-.22-.41-.09-.41.09zm13.09 3.95l.28.06.32.12.35.18.36.27.36.35.35.47.32.59.28.73.21.88.14 1.04.05 1.23-.06 1.23-.16 1.04-.24.86-.32.71-.36.57-.4.45-.42.33-.42.24-.4.16-.36.09-.32.05-.24.02-.16-.01h-8.22v.82h5.84l.01 2.76.02.36-.05.34-.11.31-.17.29-.25.25-.31.24-.38.2-.44.17-.51.15-.58.13-.64.09-.71.07-.77.04-.84.01-1.27-.04-1.07-.14-.9-.2-.73-.25-.59-.3-.45-.33-.34-.34-.25-.34-.16-.33-.1-.3-.04-.25-.02-.2.01-.13v-5.34l.05-.64.13-.54.21-.46.26-.38.3-.32.33-.24.35-.2.35-.14.33-.1.3-.06.26-.04.21-.02.13-.01h5.84l.69-.05.59-.14.5-.21.41-.28.33-.32.27-.35.2-.36.15-.36.1-.35.07-.32.04-.28.02-.21V6.07h2.09l.14.01zm-6.47 14.25l-.23.33-.08.41.08.41.23.33.33.23.41.08.41-.08.33-.23.23-.33.08-.41-.08-.41-.23-.33-.33-.23-.41-.08-.41.08z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined),\n        'PostgreSQL': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cloud_Code2_Container_Database_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"w-6 h-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n            lineNumber: 39,\n            columnNumber: 19\n        }, undefined),\n        'Docker': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cloud_Code2_Container_Database_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-6 h-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n            lineNumber: 40,\n            columnNumber: 15\n        }, undefined),\n        'AWS': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cloud_Code2_Container_Database_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"w-6 h-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n            lineNumber: 41,\n            columnNumber: 12\n        }, undefined),\n        'Open Source': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cloud_Code2_Container_Database_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"w-6 h-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n            lineNumber: 42,\n            columnNumber: 20\n        }, undefined)\n    };\n    return iconMap[name] || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cloud_Code2_Container_Database_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        className: \"w-6 h-6\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n        lineNumber: 45,\n        columnNumber: 51\n    }, undefined);\n};\n_c = TechIcon;\nfunction AboutSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"about\",\n        className: \"px-6 lg:px-12 mt-24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-2 gap-12 lg:gap-16 items-start\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SmoothReveal, {\n                                direction: \"up\",\n                                delay: 0.1,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl lg:text-5xl font-bold text-deep-charcoal dark:text-dark-text mb-8\",\n                                        children: \"Who Am I?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 text-deep-charcoal/80 dark:text-dark-text/80 leading-relaxed animate-slide-up\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg\",\n                                        children: \"I'm a passionate Full Stack Engineer with a love for creating meaningful software solutions. When I'm not coding, you'll find me exploring new technologies, contributing to open-source projects, or spending quality time with my furry companion.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg\",\n                                        children: \"My journey in software development started with curiosity and has evolved into a mission to build tools that make developers' lives easier and more productive. I believe in the power of clean code, thoughtful design, and collaborative development.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg\",\n                                        children: \"Beyond the technical realm, I enjoy writing about my experiences, sharing knowledge with the community, and mentoring aspiring developers. Every project is an opportunity to learn something new and push the boundaries of what's possible.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SmoothReveal, {\n                                direction: \"up\",\n                                delay: 0.4,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-deep-charcoal dark:text-dark-text mb-4\",\n                                            children: \"What I Love Working With\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StaggerContainer, {\n                                            className: \"flex flex-wrap gap-2 sm:gap-4\",\n                                            stagger: 0.05,\n                                            children: [\n                                                'React',\n                                                'Next.js',\n                                                'TypeScript',\n                                                'Node.js',\n                                                'Python',\n                                                'PostgreSQL',\n                                                'Docker',\n                                                'AWS',\n                                                'Open Source',\n                                                'Technical Writing'\n                                            ].map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TiltCard, {\n                                                    maxTilt: 5,\n                                                    className: \"group flex items-center gap-2 sm:gap-3 px-3 py-2 sm:px-4 sm:py-3 bg-accent-green/10 hover:bg-accent-green/20 text-accent-green rounded-lg sm:rounded-xl border border-accent-green/20 hover:border-accent-green/40 transition-all duration-300 hover:scale-105\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-accent-green group-hover:scale-110 transition-transform duration-300\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-4 h-4 sm:w-6 sm:h-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechIcon, {\n                                                                    name: skill\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                                    lineNumber: 102,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                                lineNumber: 101,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs sm:text-sm font-medium\",\n                                                            children: skill\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                            lineNumber: 105,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, skill, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ParallaxElement, {\n                                speed: 0.4,\n                                direction: \"up\",\n                                scale: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-full h-64 lg:h-90\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        src: \"/cuto-3.png\",\n                                        alt: \"Character with cat illustration\",\n                                        fill: true,\n                                        className: \"object-contain\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-light-almond/50 dark:bg-dark-surface/50 rounded-2xl p-4 lg:p-1 animate-fade-in\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-accent-green rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 text-white\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-deep-charcoal dark:text-dark-text\",\n                                                            children: \"Problem Solver\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-deep-charcoal/70 dark:text-dark-text/70\",\n                                                            children: \"I love tackling complex challenges and finding elegant solutions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-accent-green rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 text-white\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-deep-charcoal dark:text-dark-text\",\n                                                            children: \"Continuous Learner\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-deep-charcoal/70 dark:text-dark-text/70\",\n                                                            children: \"Always exploring new technologies and best practices\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-accent-green rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 text-white\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 8H16c-.8 0-1.54.37-2.01.99L12 11l-1.99-2.01A2.5 2.5 0 0 0 8 8H5.46c-.8 0-1.54.37-2.01.99L1 12v10h2v-6h2.5l-1.5-4.5h2L8 18h8l2-6.5h2L18.5 16H21v6h2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-deep-charcoal dark:text-dark-text\",\n                                                            children: \"Team Player\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-deep-charcoal/70 dark:text-dark-text/70\",\n                                                            children: \"Collaboration and knowledge sharing drive the best results\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_c1 = AboutSection;\nvar _c, _c1;\n$RefreshReg$(_c, \"TechIcon\");\n$RefreshReg$(_c1, \"AboutSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/AboutSection.tsx\n"));

/***/ })

});